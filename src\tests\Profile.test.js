import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import Profile from '../pages/Profile'
import * as auth from '../api/auth'

// Mock the auth module
vi.mock('../api/auth', () => ({
  getUser: vi.fn(),
  getToken: vi.fn(),
  updateProfile: vi.fn(),
  updatePassword: vi.fn(),
  setPassword: vi.fn()
}))

// Mock the API client
vi.mock('../api/apiClient', () => ({
  default: {
    get: vi.fn()
  }
}))

const renderProfile = () => {
  return render(
    <BrowserRouter>
      <Profile />
    </BrowserRouter>
  )
}

describe('Profile Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock user data
    auth.getUser.mockReturnValue({
      id: 1,
      name: 'محمد أحمد',
      phone: '0500000001',
      role: 'client'
    })
    auth.getToken.mockReturnValue('mock-token')
  })

  it('should render profile form with user data', () => {
    renderProfile()
    
    expect(screen.getByDisplayValue('محمد أحمد')).toBeInTheDocument()
    expect(screen.getByDisplayValue('0500000001')).toBeInTheDocument()
    expect(screen.getByText('الملف الشخصي')).toBeInTheDocument()
  })

  it('should show error when name is empty', async () => {
    renderProfile()
    
    const nameInput = screen.getByDisplayValue('محمد أحمد')
    const saveButton = screen.getByText('حفظ')
    
    fireEvent.change(nameInput, { target: { value: '' } })
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('يرجى إدخال الاسم')).toBeInTheDocument()
    })
  })

  it('should show error when name looks like phone number', async () => {
    renderProfile()
    
    const nameInput = screen.getByDisplayValue('محمد أحمد')
    const saveButton = screen.getByText('حفظ')
    
    fireEvent.change(nameInput, { target: { value: '0500000001' } })
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('الاسم لا يمكن أن يكون رقم الجوال أو أرقامًا فقط')).toBeInTheDocument()
    })
  })

  it('should successfully update profile', async () => {
    const mockUpdatedUser = {
      id: 1,
      name: 'أحمد محمد',
      phone: '0500000001',
      role: 'client'
    }
    
    auth.updateProfile.mockResolvedValue(mockUpdatedUser)
    
    renderProfile()
    
    const nameInput = screen.getByDisplayValue('محمد أحمد')
    const saveButton = screen.getByText('حفظ')
    
    fireEvent.change(nameInput, { target: { value: 'أحمد محمد' } })
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(auth.updateProfile).toHaveBeenCalledWith({ name: 'أحمد محمد' })
      expect(screen.getByText('تم حفظ التعديلات بنجاح')).toBeInTheDocument()
    })
  })

  it('should validate password requirements', async () => {
    renderProfile()
    
    const passwordInput = screen.getByLabelText('كلمة مرور جديدة')
    const confirmInput = screen.getByLabelText('تأكيد كلمة المرور')
    const saveButton = screen.getByText('حفظ')
    
    // Test short password
    fireEvent.change(passwordInput, { target: { value: '123' } })
    fireEvent.change(confirmInput, { target: { value: '123' } })
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('كلمة المرور يجب ألا تقل عن 6 أحرف')).toBeInTheDocument()
    })
    
    // Test password mismatch
    fireEvent.change(passwordInput, { target: { value: '123456' } })
    fireEvent.change(confirmInput, { target: { value: '654321' } })
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('تأكيد كلمة المرور غير مطابق')).toBeInTheDocument()
    })
  })

  it('should show loading state during save', async () => {
    auth.updateProfile.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))
    
    renderProfile()
    
    const saveButton = screen.getByText('حفظ')
    fireEvent.click(saveButton)
    
    expect(screen.getByText('جاري الحفظ...')).toBeInTheDocument()
    expect(saveButton).toBeDisabled()
    
    await waitFor(() => {
      expect(screen.getByText('حفظ')).toBeInTheDocument()
    })
  })

  it('should handle API errors gracefully', async () => {
    auth.updateProfile.mockRejectedValue(new Error('Network error'))
    
    renderProfile()
    
    const saveButton = screen.getByText('حفظ')
    fireEvent.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText(/حدث خطأ أثناء حفظ التعديلات/)).toBeInTheDocument()
    })
  })
})
