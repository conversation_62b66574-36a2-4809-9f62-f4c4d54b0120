import React from 'react'
import { getUser, setPassword, getToken, updateProfile, updatePassword } from '../api/auth'
import api from '../api/apiClient'

function deriveName(u){
  if(!u) return ''
  const looksLikePhone = (val)=> typeof val === 'string' && /^[0-9+\s-]+$/.test(val.trim())
  const clean = (val)=> (typeof val === 'string' ? val.trim() : '')

  const first = clean(u.firstName || u.given_name || u.givenName)
  const last  = clean(u.lastName  || u.family_name || u.familyName)
  if(first || last){
    const full = `${first} ${last}`.trim()
    if(full && !looksLikePhone(full) && full !== u.phone) return full
  }

  const candidates = [
    u.name, u.fullName, u.full_name, u.username, u.displayName, u.display_name,
    u.userName, u.UserName, u.Name,
    u.user?.name, u.profile?.name,
    u.client?.name, u.manager?.name, u.admin?.name,
  ].map(clean).filter(Boolean)

  for(const c of candidates){
    if(c && !looksLikePhone(c) && c !== u.phone) return c
  }

  // لا نرجع رقم الجوال كاسم إطلاقًا في صفحة الملف الشخصي
  return ''
}

function base64UrlDecode(input){
  try{
    const b64 = input.replace(/-/g,'+').replace(/_/g,'/') + '==='.slice((input.length+3)%4)
    const bin = atob(b64)
    // Decode UTF-8 bytes correctly
    try{
      const bytes = Uint8Array.from(bin, c=>c.charCodeAt(0))
      return new TextDecoder('utf-8').decode(bytes)
    }catch{
      // Fallback for older browsers
      return decodeURIComponent(escape(bin))
    }
  }catch{ return '' }
}

function deriveNameFromToken(token){
  if(!token) return ''
  try{
    const parts = token.split('.')
    if(parts.length < 2) return ''
    const payload = JSON.parse(base64UrlDecode(parts[1])||'{}')
    const candidates = [
      payload.name, payload.fullName, payload.full_name,
      payload.preferred_username, payload.username,
      `${payload.given_name||''} ${payload.family_name||''}`.trim(),
      payload.sub,
    ].filter(v=>typeof v==='string' && v.trim())
    for(const c of candidates){
      const v = c.trim()
      if(v && !/^[0-9+\s-]+$/.test(v)) return v
    }
  }catch{}
  return ''
}

export default function Profile(){
  const [user, setUser] = React.useState(getUser())
  const initialUser = getUser()
  const [name, setName] = React.useState(deriveName(initialUser) || '')
  const [phone] = React.useState(initialUser?.phone || '')
  const [pwd, setPwd] = React.useState('')
  const [confirm, setConfirm] = React.useState('')
  const [msg, setMsg] = React.useState('')
  const [loading, setLoading] = React.useState(false)
  const looksLikePhone = (val)=> typeof val === 'string' && /^[0-9+\s-]+$/.test(val.trim())

  // إذا تغير المستخدم في الذاكرة، حدث الاسم المشتق
  React.useEffect(()=>{
    const derived = deriveName(user)
    if(!name && derived) setName(derived)
  }, [user])

  // جلب بيانات المستخدم الحقيقية من الباكند (إن توفرت صلاحية) لتحديث الاسم والحقول
  React.useEffect(()=>{
    let mounted = true
    ;(async()=>{
      try{
        const res = await api.get('/auth/me')
        const u = res?.data?.user || res?.data || null
        if(mounted && u){
          localStorage.setItem('user', JSON.stringify(u))
          setUser(u)
          setName(deriveName(u) || name)
        }
      }catch(e){ /* ignore if endpoint not available */ }
      // fallback: استخراج الاسم من التوكن إن لم يتوفر من الباكند/المستخدم
      if(mounted && !name){
        const tokName = deriveNameFromToken(getToken())
        if(tokName) setName(tokName)
      }
    })()
    return ()=>{ mounted=false }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const save = async (e) => {
    e.preventDefault(); setMsg(''); setLoading(true)

    if(!name || !name.trim()){
      setLoading(false)
      return setMsg('يرجى إدخال الاسم')
    }
    if(looksLikePhone(name) || name.trim() === phone){
      setLoading(false)
      return setMsg('الاسم لا يمكن أن يكون رقم الجوال أو أرقامًا فقط')
    }

    try {
      // تحديث الاسم
      const profileData = { name: name.trim() }
      const updatedUser = await updateProfile(profileData)
      setUser(updatedUser)

      // تحديث كلمة المرور إذا تم إدخالها
      if (pwd) {
        if (pwd.length < 6) {
          setLoading(false)
          return setMsg('كلمة المرور يجب ألا تقل عن 6 أحرف')
        }
        if (pwd !== confirm) {
          setLoading(false)
          return setMsg('تأكيد كلمة المرور غير مطابق')
        }
        if(!phone){
          setLoading(false)
          return setMsg('لا يمكن تغيير كلمة المرور لعدم توفر رقم الجوال')
        }

        try {
          await updatePassword('', pwd) // كلمة المرور الحالية فارغة للتوافق مع النظام الحالي
          setPwd('')
          setConfirm('')
          setMsg('تم حفظ التعديلات وتحديث كلمة المرور بنجاح')
        } catch (passwordError) {
          console.warn('Password update failed, using fallback:', passwordError)
          setPassword(phone, pwd)
          setPwd('')
          setConfirm('')
          setMsg('تم حفظ التعديلات وتحديث كلمة المرور محلياً')
        }
      } else {
        setMsg('تم حفظ التعديلات بنجاح')
      }

    } catch (error) {
      console.error('Profile update error:', error)
      setMsg('حدث خطأ أثناء حفظ التعديلات: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className='max-w-2xl mx-auto card p-6'>
      <h1 className='heading-gold text-2xl font-bold mb-4'>الملف الشخصي</h1>
      {msg && (
        <div className={`mb-4 p-3 border rounded ${
          msg.includes('خطأ') || msg.includes('يرجى') || msg.includes('لا يمكن')
            ? 'bg-red-100 border-red-200 text-red-800'
            : 'bg-green-100 border-green-200 text-green-800'
        }`}>
          {msg}
        </div>
      )}
      <form onSubmit={save} className='space-y-4'>
        <div>
          <label className='block text-sm mb-2'>الاسم</label>
          <input value={name} onChange={e=>setName(e.target.value)} className='w-full border p-2 rounded' placeholder='اكتب اسمك هنا' />
        </div>
        <div>
          <label className='block text-sm mb-2'>رقم الجوال</label>
          <input value={phone} disabled className='w-full border p-2 rounded bg-gray-50 text-gray-600' />
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div>
            <label className='block text-sm mb-2'>كلمة مرور جديدة</label>
            <input type='password' value={pwd} onChange={e=>setPwd(e.target.value)} className='w-full border p-2 rounded' />
          </div>
          <div>
            <label className='block text-sm mb-2'>تأكيد كلمة المرور</label>
            <input type='password' value={confirm} onChange={e=>setConfirm(e.target.value)} className='w-full border p-2 rounded' />
          </div>
        </div>
        <button
          type='submit'
          disabled={loading}
          className={`px-4 py-2 rounded ${loading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'btn-primary hover:bg-blue-600'
          }`}
        >
          {loading ? 'جاري الحفظ...' : 'حفظ'}
        </button>
      </form>
    </div>
  )
}