// Auth API - mocked for frontend-only usage
import api from './apiClient'

// Hardcoded demo users (phone -> user)
const demoUsers = {
  '0500000001': { id: 1, name: 'مدير التطبيق', role: 'admin', phone: '0500000001' },
  '0500000002': { id: 2, name: 'المدير العام', role: 'manager', phone: '0500000002' },
  '0500000003': { id: 3, name: 'المسؤول عن المسح', role: 'scanner', phone: '0500000003' },
  '0500000004': { id: 4, name: 'عميل', role: 'client', phone: '0500000004' },
}

function getPasswordsMap(){
  try{ return JSON.parse(localStorage.getItem('passwords')||'{}') }catch{ return {} }
}
function setPasswordsMap(map){ localStorage.setItem('passwords', JSON.stringify(map)) }

export async function login(phone, password){
  try{
    const res = await api.post('/auth/login', { phone, password })
    const data = res.data
    if(data?.tokens?.accessToken){
      localStorage.setItem('access_token', data.tokens.accessToken)
      localStorage.setItem('user', JSON.stringify(data.user))
    }
    return data
  }catch(err){
    throw err
  }
}

export async function register(payload){
  try{
    const res = await api.post('/auth/register', payload)
    return res.data
  }catch(err){
    throw err
  }
}

export function setPassword(phone, newPassword){
  // Local helper until backend reset is wired
  try{
    const map = JSON.parse(localStorage.getItem('passwords')||'{}')
    map[phone] = newPassword
    localStorage.setItem('passwords', JSON.stringify(map))
  }catch{}
}

export async function updateProfile(userData){
  try{
    // محاولة إرسال التحديث للباكند
    const res = await api.put('/auth/profile', userData)
    const updatedUser = res.data?.user || res.data

    // تحديث البيانات المحلية
    if(updatedUser){
      localStorage.setItem('user', JSON.stringify(updatedUser))
      return updatedUser
    }
    return userData
  }catch(err){
    // في حالة عدم توفر الباكند، احفظ محلياً
    console.warn('Backend not available, saving locally:', err.message)
    const currentUser = getUser()
    const updated = { ...currentUser, ...userData }
    localStorage.setItem('user', JSON.stringify(updated))
    return updated
  }
}

export async function updatePassword(currentPassword, newPassword){
  try{
    const res = await api.put('/auth/password', {
      currentPassword,
      newPassword
    })
    return res.data
  }catch(err){
    // Fallback للحفظ المحلي
    const user = getUser()
    if(user?.phone){
      setPassword(user.phone, newPassword)
      return { success: true, message: 'تم تحديث كلمة المرور محلياً' }
    }
    throw err
  }
}

export function logout(){
  try{ api.get('/auth/logout') }catch(e){}
  localStorage.removeItem('access_token'); localStorage.removeItem('user')
}

export function getToken(){ return localStorage.getItem('access_token') }
export function getUser(){ try{return JSON.parse(localStorage.getItem('user'))}catch{return null} }
